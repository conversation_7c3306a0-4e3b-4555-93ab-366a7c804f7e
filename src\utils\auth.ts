import { history, request } from '@umijs/max';

const TOKEN_KEY = 'zhyj_access_token';
const REFRESH_TOKEN_KEY = 'zhyj_refresh_token';

// 用于存储刷新token的Promise
let refreshTokenPromise: Promise<string | null> | null = null;

export function getToken(): string | null {
  return localStorage.getItem(TOKEN_KEY);
}

export function getRefreshToken(): string | null {
  return localStorage.getItem(REFRESH_TOKEN_KEY);
}

export function setTokens(token: string, refreshToken: string) {
  localStorage.setItem(TOKEN_KEY, token);
  localStorage.setItem(REFRESH_TOKEN_KEY, refreshToken);
}

export function removeTokens() {
  localStorage.removeItem(TOKEN_KEY);
  localStorage.removeItem(REFRESH_TOKEN_KEY);
  refreshTokenPromise = null;
}

// 刷新token
export async function refreshToken(): Promise<string | null> {
  try {
    // 如果已经在刷新，返回正在进行的Promise
    if (refreshTokenPromise) {
      return refreshTokenPromise;
    }

    const refreshToken = getRefreshToken();
    if (!refreshToken) {
      throw new Error('No refresh token');
    }

    // 创建新的刷新Promise
    refreshTokenPromise = new Promise((resolve, reject) => {
      (async () => {
        try {
          const res = await request<API.ResType<{ token: string }>>(
            '/auth/refresh',
            {
              method: 'POST',
              data: { refreshToken },
            },
          );

          if (res.errCode === 0 && res.data?.token) {
            setTokens(res.data.token, refreshToken);
            resolve(res.data.token);
          } else {
            // 刷新失败，直接抛出错误
            throw new Error(res.msg || '刷新token失败');
          }
        } catch (error) {
          reject(error);
        } finally {
          // 完成后清空Promise
          refreshTokenPromise = null;
        }
      })();
    });

    return refreshTokenPromise;
  } catch (error) {
    // 发生错误时清除tokens
    removeTokens();
    throw error; // 向上抛出错误，让调用方处理跳转
  }
}

export async function logout() {
  removeTokens();
  history.push('/login');
}
