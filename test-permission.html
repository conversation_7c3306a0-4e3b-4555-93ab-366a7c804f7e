<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>权限系统测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            background: #f5f5f5;
            padding: 20px;
            margin: 15px 0;
            border-radius: 8px;
            border-left: 4px solid #1890ff;
        }
        .test-case {
            background: white;
            padding: 15px;
            margin: 10px 0;
            border-radius: 6px;
            border: 1px solid #e8e8e8;
        }
        .success {
            border-left-color: #52c41a;
            background: #f6ffed;
        }
        .warning {
            border-left-color: #faad14;
            background: #fffbe6;
        }
        .error {
            border-left-color: #ff4d4f;
            background: #fff2f0;
        }
        button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover {
            background: #40a9ff;
        }
        button.danger {
            background: #ff4d4f;
        }
        button.danger:hover {
            background: #ff7875;
        }
        .code {
            background: #f6f8fa;
            padding: 12px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            border: 1px solid #e1e4e8;
        }
        .permission-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        .permission-table th,
        .permission-table td {
            border: 1px solid #e8e8e8;
            padding: 12px;
            text-align: left;
        }
        .permission-table th {
            background: #fafafa;
            font-weight: bold;
        }
        .yes {
            color: #52c41a;
            font-weight: bold;
        }
        .no {
            color: #ff4d4f;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>🔐 权限系统测试指南</h1>
    
    <div class="test-section">
        <h2>📋 测试账号信息</h2>
        <div class="test-case success">
            <h3>管理员账号</h3>
            <div class="code">
                用户名: admin<br>
                密码: admin123<br>
                角色: 管理员<br>
                权限: 所有权限
            </div>
        </div>
        <div class="test-case warning">
            <h3>编辑员账号</h3>
            <div class="code">
                用户名: editor<br>
                密码: 123456<br>
                角色: 编辑员<br>
                权限: 编辑权限（不能管理用户和字典）
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>🎯 权限矩阵</h2>
        <table class="permission-table">
            <thead>
                <tr>
                    <th>功能模块</th>
                    <th>管理员</th>
                    <th>编辑员</th>
                    <th>未登录</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>仪表盘</td>
                    <td class="yes">✓</td>
                    <td class="yes">✓</td>
                    <td class="no">✗</td>
                </tr>
                <tr>
                    <td>山塬管理</td>
                    <td class="yes">✓</td>
                    <td class="yes">✓</td>
                    <td class="no">✗</td>
                </tr>
                <tr>
                    <td>水系管理</td>
                    <td class="yes">✓</td>
                    <td class="yes">✓</td>
                    <td class="no">✗</td>
                </tr>
                <tr>
                    <td>历史要素管理</td>
                    <td class="yes">✓</td>
                    <td class="yes">✓</td>
                    <td class="no">✗</td>
                </tr>
                <tr>
                    <td>字典管理</td>
                    <td class="yes">✓</td>
                    <td class="no">✗</td>
                    <td class="no">✗</td>
                </tr>
                <tr>
                    <td>资源管理</td>
                    <td class="yes">✓</td>
                    <td class="yes">✓</td>
                    <td class="no">✗</td>
                </tr>
                <tr>
                    <td>用户管理</td>
                    <td class="yes">✓</td>
                    <td class="no">✗</td>
                    <td class="no">✗</td>
                </tr>
            </tbody>
        </table>
    </div>

    <div class="test-section">
        <h2>🧪 测试步骤</h2>
        
        <div class="test-case">
            <h3>1. 未登录访问测试</h3>
            <p><strong>目标：</strong>验证未登录用户无法访问管理端</p>
            <p><strong>步骤：</strong></p>
            <ol>
                <li>确保已退出登录（清除浏览器缓存）</li>
                <li>直接访问管理端页面</li>
                <li>验证是否自动跳转到登录页</li>
            </ol>
            <p>
                <button onclick="window.open('http://localhost:8000/admin/dashboard', '_blank')">
                    测试访问仪表盘
                </button>
                <button onclick="window.open('http://localhost:8000/admin/user', '_blank')">
                    测试访问用户管理
                </button>
            </p>
        </div>

        <div class="test-case">
            <h3>2. 管理员权限测试</h3>
            <p><strong>目标：</strong>验证管理员可以访问所有功能</p>
            <p><strong>步骤：</strong></p>
            <ol>
                <li>使用管理员账号登录 (admin/admin123)</li>
                <li>验证可以访问所有管理页面</li>
                <li>检查左侧菜单是否显示所有功能模块</li>
            </ol>
            <p>
                <button onclick="window.open('http://localhost:8000/admin/login', '_blank')">
                    打开登录页
                </button>
            </p>
        </div>

        <div class="test-case">
            <h3>3. 编辑员权限测试</h3>
            <p><strong>目标：</strong>验证编辑员只能访问有权限的功能</p>
            <p><strong>步骤：</strong></p>
            <ol>
                <li>使用编辑员账号登录 (editor/123456)</li>
                <li>验证可以访问数据管理和资源管理</li>
                <li>尝试访问用户管理，应该显示403页面</li>
                <li>尝试访问字典管理，应该显示403页面</li>
            </ol>
            <p>
                <button onclick="testEditorPermission()">
                    开始编辑员权限测试
                </button>
            </p>
        </div>

        <div class="test-case">
            <h3>4. 403页面测试</h3>
            <p><strong>目标：</strong>验证无权限时正确显示403页面</p>
            <p><strong>步骤：</strong></p>
            <ol>
                <li>使用编辑员账号登录</li>
                <li>直接访问无权限的页面</li>
                <li>验证是否显示403无权限页面</li>
                <li>验证页面上的操作按钮是否正常</li>
            </ol>
            <p>
                <button onclick="window.open('http://localhost:8000/403', '_blank')">
                    直接查看403页面
                </button>
            </p>
        </div>
    </div>

    <div class="test-section">
        <h2>🔍 快速测试链接</h2>
        
        <div class="test-case">
            <h3>登录相关</h3>
            <p>
                <button onclick="window.open('http://localhost:8000/admin/login', '_blank')">
                    登录页面
                </button>
                <button onclick="clearStorage()">
                    清除登录状态
                </button>
            </p>
        </div>

        <div class="test-case">
            <h3>管理页面</h3>
            <p>
                <button onclick="window.open('http://localhost:8000/admin/dashboard', '_blank')">
                    仪表盘
                </button>
                <button onclick="window.open('http://localhost:8000/admin/mountain', '_blank')">
                    山塬管理
                </button>
                <button onclick="window.open('http://localhost:8000/admin/user', '_blank')">
                    用户管理
                </button>
                <button onclick="window.open('http://localhost:8000/admin/dictionary', '_blank')">
                    字典管理
                </button>
            </p>
        </div>

        <div class="test-case">
            <h3>错误页面</h3>
            <p>
                <button onclick="window.open('http://localhost:8000/403', '_blank')">
                    403无权限页面
                </button>
            </p>
        </div>
    </div>

    <div class="test-section">
        <h2>✅ 验证清单</h2>
        
        <div class="test-case">
            <h3>基础功能</h3>
            <ul>
                <li>□ 未登录访问管理端自动跳转登录页</li>
                <li>□ 登录成功后跳转到仪表盘</li>
                <li>□ 退出登录后清除状态并跳转登录页</li>
                <li>□ 页面刷新后保持登录状态</li>
            </ul>
        </div>

        <div class="test-case">
            <h3>权限控制</h3>
            <ul>
                <li>□ 管理员可以访问所有功能</li>
                <li>□ 编辑员不能访问用户管理</li>
                <li>□ 编辑员不能访问字典管理</li>
                <li>□ 编辑员可以访问数据管理功能</li>
                <li>□ 无权限时显示403页面</li>
                <li>□ 403页面的操作按钮正常工作</li>
            </ul>
        </div>

        <div class="test-case">
            <h3>用户体验</h3>
            <ul>
                <li>□ 左侧菜单根据权限显示/隐藏</li>
                <li>□ 权限不足时有明确的提示信息</li>
                <li>□ 页面跳转流畅，无明显延迟</li>
                <li>□ 错误处理友好，有相应的提示</li>
            </ul>
        </div>
    </div>

    <script>
        function clearStorage() {
            localStorage.clear();
            sessionStorage.clear();
            alert('已清除所有本地存储数据，请刷新页面');
        }

        function testEditorPermission() {
            alert('请按以下步骤测试：\n\n1. 使用 editor/123456 登录\n2. 尝试访问用户管理页面\n3. 验证是否显示403页面\n4. 测试403页面的返回按钮');
        }
    </script>
</body>
</html>
