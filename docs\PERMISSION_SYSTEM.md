# 权限管理系统说明

## 概述

智慧营建系统实现了完善的权限管理机制，包括登录验证、角色权限控制、页面访问控制等功能。

## 权限体系

### 用户角色

1. **管理员 (admin)**
   - 拥有所有权限
   - 可以管理用户、字典、资源等
   - 可以访问所有管理功能

2. **编辑员 (editor)**
   - 拥有编辑权限
   - 可以管理山塬、水系、历史要素数据
   - 可以管理资源文件
   - 不能管理用户和字典

### 权限定义

在 `src/access.ts` 中定义了以下权限：

```typescript
{
  // 基础权限：是否已登录
  isLoggedIn: !!currentUser,
  
  // 管理员权限：需要登录且是管理员角色
  canAdmin: isAdmin && currentUser,
  
  // 编辑权限：需要登录且是管理员或编辑员
  canEdit: currentUser && (currentUser.role === 'admin' || currentUser.role === 'editor'),
  
  // 查看权限：已登录用户都可以查看
  canView: !!currentUser,
  
  // 用户管理权限：只有管理员可以管理用户
  canManageUsers: currentUser && currentUser.role === 'admin',
  
  // 字典管理权限：只有管理员可以管理字典
  canManageDictionary: currentUser && currentUser.role === 'admin',
  
  // 资源管理权限：管理员和编辑员都可以管理资源
  canManageResources: currentUser && (currentUser.role === 'admin' || currentUser.role === 'editor'),
}
```

## 页面权限控制

### 路由级权限

在 `config/routes.ts` 中为每个路由配置了相应的权限：

```typescript
{
  path: '/admin/dashboard',
  access: 'canView',        // 所有登录用户可访问
},
{
  path: '/admin/mountain',
  access: 'canEdit',        // 管理员和编辑员可访问
},
{
  path: '/admin/user',
  access: 'canManageUsers', // 只有管理员可访问
}
```

### 权限检查流程

1. **路由变化检查** (`src/app.ts` 中的 `onRouteChange`)
   - 检查是否访问管理端路由
   - 验证用户是否已登录
   - 未登录自动跳转到登录页

2. **Umi Access 权限检查**
   - 基于路由配置的 `access` 字段
   - 自动检查用户权限
   - 无权限时显示403页面

3. **组件级权限检查**
   - 使用 `PermissionWrapper` 组件
   - 在组件内部进行细粒度权限控制

## 权限检查组件

### PermissionWrapper

用于在组件级别进行权限控制：

```tsx
import PermissionWrapper from '@/components/PermissionWrapper';

<PermissionWrapper permission="canManageUsers">
  <UserManagementComponent />
</PermissionWrapper>
```

**参数说明：**
- `permission`: 权限名称
- `fallback`: 无权限时显示的组件
- `redirect`: 无权限时跳转的路径（默认 `/403`）

## 403无权限页面

位置：`src/pages/403.tsx`

**功能：**
- 显示无权限提示信息
- 根据用户登录状态显示不同的操作按钮
- 已登录用户：返回首页
- 未登录用户：立即登录 + 返回首页

## 使用示例

### 1. 在页面中使用权限检查

```tsx
import { useAccess } from '@umijs/max';

const MyComponent = () => {
  const access = useAccess();
  
  return (
    <div>
      {access.canManageUsers && (
        <Button>管理用户</Button>
      )}
      {access.canEdit && (
        <Button>编辑数据</Button>
      )}
    </div>
  );
};
```

### 2. 使用权限包装组件

```tsx
import PermissionWrapper from '@/components/PermissionWrapper';

const AdminPanel = () => {
  return (
    <PermissionWrapper permission="canAdmin">
      <div>只有管理员可以看到这里</div>
    </PermissionWrapper>
  );
};
```

### 3. 在菜单中使用权限

```tsx
const menuItems = [
  {
    key: 'dashboard',
    label: '仪表盘',
    // 所有登录用户可见
  },
  {
    key: 'users',
    label: '用户管理',
    access: 'canManageUsers', // 只有管理员可见
  },
];
```

## 测试场景

### 1. 未登录访问测试

```
1. 直接访问 http://localhost:8000/admin/dashboard
2. 应该自动跳转到 http://localhost:8000/admin/login
3. 登录成功后跳转到仪表盘
```

### 2. 权限不足测试

```
1. 使用编辑员账号登录 (editor/123456)
2. 尝试访问 http://localhost:8000/admin/user
3. 应该显示403无权限页面
4. 点击"返回首页"按钮跳转到仪表盘
```

### 3. 管理员权限测试

```
1. 使用管理员账号登录 (admin/admin123)
2. 可以访问所有管理页面
3. 左侧菜单显示所有功能模块
```

### 4. 编辑员权限测试

```
1. 使用编辑员账号登录 (editor/123456)
2. 可以访问仪表盘、数据管理、资源管理
3. 不能访问用户管理、字典管理
4. 左侧菜单只显示有权限的功能模块
```

## 安全特性

### 1. Token验证
- 所有管理端请求都需要携带有效token
- Token过期自动刷新或跳转登录
- 退出登录时清除所有本地状态

### 2. 路由保护
- 管理端路由需要登录验证
- 基于角色的页面访问控制
- 无权限时自动跳转403页面

### 3. 状态管理
- 用户状态存储在全局 initialState 中
- 页面刷新时自动恢复登录状态
- 权限变更时实时更新界面

## 扩展说明

### 添加新权限

1. 在 `src/access.ts` 中添加权限定义
2. 在路由配置中使用新权限
3. 在组件中使用 `useAccess()` 检查权限

### 添加新角色

1. 更新用户数据结构
2. 在 `src/access.ts` 中添加角色判断
3. 更新相关权限逻辑

### 自定义403页面

修改 `src/pages/403.tsx` 文件，自定义无权限页面的样式和内容。
