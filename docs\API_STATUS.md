# API接口状态说明

## 当前状态

✅ **登录功能已实现真实API调用**

系统现在确实向后台发送真实的HTTP请求，而不是假的登录。

## 技术实现

### 1. API服务层
位置：`src/services/auth.ts`

```typescript
export async function login(params: {
  username: string;
  password: string;
}) {
  return request('/admin/auth/login', {
    method: 'POST',
    data: params,
  });
}
```

### 2. 登录页面调用
位置：`src/pages/Admin/Login/index.tsx`

```typescript
const response = await login(values);
if (response.errCode === 0 && response.data) {
  setToken(response.data.token);
  // 更新全局状态...
}
```

### 3. Mock API实现
位置：`mock/authAPI.ts`

- 模拟真实的后台API行为
- 生成JWT Token
- 完整的参数验证
- 错误处理

## 请求流程

1. **用户输入账号密码** → 前端表单
2. **调用login API** → `src/services/auth.ts`
3. **发送HTTP请求** → `POST /api/admin/auth/login`
4. **Mock服务处理** → `mock/authAPI.ts`
5. **返回响应数据** → 包含token和用户信息
6. **保存token** → localStorage
7. **更新全局状态** → initialState
8. **跳转页面** → 仪表盘

## 网络请求验证

### 在浏览器开发者工具中查看

1. 打开浏览器开发者工具 (F12)
2. 切换到 "Network" 标签
3. 在登录页面输入账号密码
4. 点击登录按钮
5. 查看网络请求列表

**应该看到：**
- 请求URL: `http://localhost:8000/api/admin/auth/login`
- 请求方法: `POST`
- 请求头: `Content-Type: application/json`
- 请求体: `{"username":"admin","password":"admin123"}`
- 响应状态: `200 OK`
- 响应数据: 包含token和用户信息

### 使用API测试页面

访问：`file:///E:/codes/zhi-hui-ying-jian/zhi-hui-ying-jian-web/test-api.html`

点击"测试管理员登录"按钮，查看API响应。

## Mock vs 真实后台

### 当前配置（开发环境）
- ✅ 使用Mock API
- ✅ 完整的认证流程
- ✅ JWT Token机制
- ✅ 权限验证

### 生产环境配置
需要修改 `config/config.ts`：

```typescript
proxy: {
  '/api': {
    target: 'http://127.0.0.1:7001',  // 真实后台地址
    changeOrigin: true,
    pathRewrite: { '^/api': '' },
  },
},
```

## API接口列表

### 认证相关

1. **登录接口**
   - URL: `POST /api/admin/auth/login`
   - 参数: `{username, password}`
   - 响应: `{token, user}`

2. **获取用户信息**
   - URL: `GET /api/admin/auth/profile`
   - 头部: `Authorization: Bearer <token>`
   - 响应: `{id, username, role, permissions}`

3. **退出登录**
   - URL: `POST /api/admin/auth/logout`
   - 头部: `Authorization: Bearer <token>`
   - 响应: `{message}`

4. **刷新Token**
   - URL: `POST /api/admin/auth/refresh`
   - 头部: `Authorization: Bearer <token>`
   - 响应: `{message}` + 新token在响应头

## 测试账号

### 管理员
- 用户名: `admin`
- 密码: `admin123`
- 权限: 所有功能

### 编辑员
- 用户名: `editor`
- 密码: `123456`
- 权限: 数据编辑功能

## 验证步骤

### 1. 网络请求验证
```
1. 打开浏览器开发者工具
2. 访问登录页面
3. 输入账号密码并登录
4. 在Network标签查看HTTP请求
5. 确认请求URL、方法、参数、响应
```

### 2. Token验证
```
1. 登录成功后检查localStorage
2. 应该看到 'zhyj_access_token' 项
3. Token格式: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### 3. 状态管理验证
```
1. 登录后页面右上角显示用户信息
2. 页面刷新后状态保持
3. 退出登录后状态清除
```

### 4. 权限验证
```
1. 不同角色用户看到不同菜单
2. 无权限页面显示403错误
3. 未登录访问自动跳转登录页
```

## 常见问题

### Q: 为什么看不到网络请求？
A: 检查浏览器开发者工具的Network标签是否已打开，并确保在登录前就已经打开。

### Q: 登录失败怎么办？
A: 检查用户名密码是否正确，查看控制台错误信息。

### Q: Token无效怎么办？
A: 清除浏览器localStorage，重新登录。

### Q: 如何连接真实后台？
A: 修改config/config.ts中的proxy配置，指向真实的后台服务地址。

## 总结

✅ **登录功能完全真实**
- 发送真实HTTP请求
- 完整的认证流程
- JWT Token机制
- 权限控制系统

🔧 **开发环境使用Mock**
- 模拟真实API行为
- 便于前端开发调试
- 无需依赖后台服务

🚀 **生产环境可切换**
- 简单配置即可连接真实后台
- 保持API接口一致性
