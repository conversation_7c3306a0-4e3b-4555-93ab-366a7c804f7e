import { useAccess, useModel } from '@umijs/max';
import { Card, Descriptions, Tag, Typography } from 'antd';
import React from 'react';

const { Title, Text } = Typography;

const DebugPage: React.FC = () => {
  const { initialState } = useModel('@@initialState');
  const access = useAccess();
  const { currentUser, isAdmin } = initialState || {};

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>权限调试信息</Title>
      
      <Card title="用户信息" style={{ marginBottom: 16 }}>
        <Descriptions column={2}>
          <Descriptions.Item label="登录状态">
            {currentUser ? <Tag color="green">已登录</Tag> : <Tag color="red">未登录</Tag>}
          </Descriptions.Item>
          <Descriptions.Item label="用户ID">
            {currentUser?.id || '无'}
          </Descriptions.Item>
          <Descriptions.Item label="用户名">
            {currentUser?.username || '无'}
          </Descriptions.Item>
          <Descriptions.Item label="角色">
            {currentUser?.role || '无'}
          </Descriptions.Item>
          <Descriptions.Item label="是否管理员">
            {isAdmin ? <Tag color="blue">是</Tag> : <Tag color="orange">否</Tag>}
          </Descriptions.Item>
          <Descriptions.Item label="账户状态">
            {currentUser?.enabled ? <Tag color="green">启用</Tag> : <Tag color="red">禁用</Tag>}
          </Descriptions.Item>
        </Descriptions>
      </Card>

      <Card title="权限状态" style={{ marginBottom: 16 }}>
        <Descriptions column={2}>
          <Descriptions.Item label="isLoggedIn">
            {access.isLoggedIn ? <Tag color="green">✓</Tag> : <Tag color="red">✗</Tag>}
          </Descriptions.Item>
          <Descriptions.Item label="canView">
            {access.canView ? <Tag color="green">✓</Tag> : <Tag color="red">✗</Tag>}
          </Descriptions.Item>
          <Descriptions.Item label="canEdit">
            {access.canEdit ? <Tag color="green">✓</Tag> : <Tag color="red">✗</Tag>}
          </Descriptions.Item>
          <Descriptions.Item label="canAdmin">
            {access.canAdmin ? <Tag color="green">✓</Tag> : <Tag color="red">✗</Tag>}
          </Descriptions.Item>
          <Descriptions.Item label="canManageUsers">
            {access.canManageUsers ? <Tag color="green">✓</Tag> : <Tag color="red">✗</Tag>}
          </Descriptions.Item>
          <Descriptions.Item label="canManageDictionary">
            {access.canManageDictionary ? <Tag color="green">✓</Tag> : <Tag color="red">✗</Tag>}
          </Descriptions.Item>
          <Descriptions.Item label="canManageResources">
            {access.canManageResources ? <Tag color="green">✓</Tag> : <Tag color="red">✗</Tag>}
          </Descriptions.Item>
        </Descriptions>
      </Card>

      <Card title="原始数据">
        <Text code>
          <pre>{JSON.stringify({ initialState, access }, null, 2)}</pre>
        </Text>
      </Card>
    </div>
  );
};

export default DebugPage;
