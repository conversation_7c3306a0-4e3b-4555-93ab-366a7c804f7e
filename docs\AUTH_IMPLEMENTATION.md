# 前端认证实现调整说明

## 概述

根据后台认证管理接口说明，对前端认证实现进行了全面调整，使其与后台接口规范保持一致。

## 主要更改

### 1. 接口路径调整

**原接口路径：**
- 登录：`/auth/login`
- 获取用户信息：`/auth/profile`
- 退出登录：`/auth/logout`
- 刷新Token：`/auth/refresh`

**新接口路径：**
- 登录：`/admin/auth/login`
- 获取用户信息：`/admin/auth/profile`
- 退出登录：`/admin/auth/logout`
- 刷新Token：`/admin/auth/refresh`

### 2. 认证方式调整

**原认证方式：**
- 使用access_token和refresh_token双token机制
- 刷新token时需要传递refreshToken参数

**新认证方式：**
- 使用单一JWT Token机制
- 刷新token时不需要额外参数，通过Authorization头传递当前token
- 新token通过响应头返回

### 3. 用户数据结构调整

**原用户数据：**
```javascript
{ id: 1, username: 'admin', password: '123456', role: 'admin' }
```

**新用户数据：**
```javascript
{
  id: 1,
  username: 'admin',
  password: 'admin123',
  role: 'admin',
  enabled: true,
  createdAt: '2024-01-01T00:00:00.000Z',
  updatedAt: '2024-01-01T00:00:00.000Z'
}
```

### 4. 表单验证规则

**新增验证规则：**
- 用户名：3-50字符长度限制
- 密码：6-50字符长度限制

### 5. 错误响应格式

**统一错误响应格式：**
```json
{
  "message": "错误信息",
  "statusCode": 401,
  "timestamp": "2024-01-01T00:00:00.000Z",
  "path": "/admin/auth/login"
}
```

## 文件更改清单

### 新增文件

1. **`src/services/auth.ts`** - 认证API服务
   - 登录接口
   - 获取用户信息接口
   - 退出登录接口
   - 刷新Token接口

2. **`mock/authAPI.ts`** - 认证接口模拟
   - 模拟后台认证接口行为
   - JWT Token生成和验证
   - 完整的错误处理

3. **`docs/AUTH_TEST.md`** - 认证功能测试文档
   - 测试步骤说明
   - API接口测试方法
   - 预期结果示例

### 修改文件

1. **`src/utils/auth.ts`**
   - 移除refresh_token相关逻辑
   - 简化token管理
   - 更新刷新token逻辑
   - 调整退出登录逻辑

2. **`src/app.ts`**
   - 更新导入的认证函数
   - 修改错误处理逻辑
   - 调整接口路径匹配
   - 更新响应拦截器
   - 增强getInitialState函数
   - 添加布局右上角用户信息显示

3. **`src/pages/Admin/Login/index.tsx`**
   - 使用新的认证API
   - 更新表单验证规则
   - 改进错误处理

4. **`src/services/mockData.ts`**
   - 更新用户数据结构
   - 调整默认管理员密码

5. **`README.md`**
   - 更新测试账号信息

## 功能特性

### 1. 自动Token刷新
- 当API返回401错误时，自动尝试刷新token
- 刷新成功后重试原请求
- 刷新失败则跳转到登录页

### 2. 用户状态管理
- 页面刷新时自动恢复用户登录状态
- 布局右上角显示当前用户信息
- 提供便捷的退出登录功能

### 3. 权限控制
- 所有管理端路由需要认证
- 未登录用户自动重定向到登录页
- 支持角色权限验证

### 4. 安全性增强
- JWT Token机制
- 请求拦截器自动添加Authorization头
- Token过期自动处理

## 测试说明

### 默认测试账号

**管理员账号：**
- 用户名：`admin`
- 密码：`admin123`
- 角色：`admin`

**编辑员账号：**
- 用户名：`editor`
- 密码：`123456`
- 角色：`editor`

### 测试步骤

1. 访问 http://localhost:8000/admin/login
2. 使用测试账号登录
3. 验证跳转到管理端首页
4. 检查右上角用户信息显示
5. 测试退出登录功能

## 部署注意事项

1. **环境配置**
   - 生产环境需要配置真实的后台API地址
   - 移除mock接口配置

2. **安全考虑**
   - 生产环境建议使用HTTPS
   - 考虑使用更安全的token存储方式
   - 定期更换默认密码

3. **性能优化**
   - 合理设置token过期时间
   - 优化API请求频率

## 兼容性说明

- 保持与现有管理端页面的兼容性
- 不影响公共网站功能
- 向后兼容现有的权限控制逻辑
