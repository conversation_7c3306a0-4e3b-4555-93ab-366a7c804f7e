<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            background: #f5f5f5;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .test-step {
            background: white;
            padding: 10px;
            margin: 5px 0;
            border-left: 4px solid #1890ff;
        }
        .success {
            border-left-color: #52c41a;
        }
        .warning {
            border-left-color: #faad14;
        }
        .error {
            border-left-color: #ff4d4f;
        }
        button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #40a9ff;
        }
        .code {
            background: #f6f8fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>管理后台登录功能测试指南</h1>
    
    <div class="test-section">
        <h2>🔐 测试账号信息</h2>
        <div class="test-step success">
            <strong>管理员账号：</strong><br>
            用户名：<code>admin</code><br>
            密码：<code>admin123</code><br>
            角色：管理员
        </div>
        <div class="test-step success">
            <strong>编辑员账号：</strong><br>
            用户名：<code>editor</code><br>
            密码：<code>123456</code><br>
            角色：编辑员
        </div>
    </div>

    <div class="test-section">
        <h2>🧪 测试步骤</h2>
        
        <div class="test-step">
            <h3>1. 登录功能测试</h3>
            <p>
                <button onclick="window.open('http://localhost:8000/admin/login', '_blank')">
                    打开登录页面
                </button>
            </p>
            <ul>
                <li>使用管理员账号登录：admin / admin123</li>
                <li>验证登录成功后跳转到仪表盘页面</li>
                <li>检查页面右上角是否显示用户信息</li>
            </ul>
        </div>

        <div class="test-step">
            <h3>2. 用户信息显示测试</h3>
            <ul>
                <li>✅ 头像显示用户名首字母（A）</li>
                <li>✅ 用户名显示为 "admin"</li>
                <li>✅ 角色显示为 "管理员"</li>
                <li>✅ 退出登录按钮正常显示</li>
            </ul>
        </div>

        <div class="test-step">
            <h3>3. 退出登录测试</h3>
            <ul>
                <li>点击右上角红色"退出登录"按钮</li>
                <li>验证是否跳转到登录页面</li>
                <li>尝试直接访问管理页面，验证是否被重定向</li>
            </ul>
        </div>

        <div class="test-step">
            <h3>4. 不同角色测试</h3>
            <ul>
                <li>使用编辑员账号登录：editor / 123456</li>
                <li>验证角色显示为 "编辑员"</li>
                <li>验证其他功能正常</li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <h2>🔍 API测试</h2>
        
        <div class="test-step">
            <h3>登录API测试</h3>
            <div class="code">
curl -X POST "http://localhost:8000/api/admin/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "admin123"
  }'
            </div>
        </div>

        <div class="test-step">
            <h3>获取用户信息API测试</h3>
            <div class="code">
curl -X GET "http://localhost:8000/api/admin/auth/profile" \
  -H "Authorization: Bearer YOUR_TOKEN"
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>📋 检查清单</h2>
        
        <div class="test-step">
            <h3>登录功能</h3>
            <ul>
                <li>□ 表单验证正常（用户名3-50字符，密码6-50字符）</li>
                <li>□ 正确账号可以成功登录</li>
                <li>□ 错误账号显示错误提示</li>
                <li>□ 登录成功后跳转到仪表盘</li>
            </ul>
        </div>

        <div class="test-step">
            <h3>用户信息显示</h3>
            <ul>
                <li>□ 右上角显示用户信息卡片</li>
                <li>□ 头像显示用户名首字母</li>
                <li>□ 用户名正确显示</li>
                <li>□ 角色正确显示</li>
                <li>□ 退出按钮正常显示</li>
            </ul>
        </div>

        <div class="test-step">
            <h3>退出登录功能</h3>
            <ul>
                <li>□ 点击退出按钮成功退出</li>
                <li>□ 退出后跳转到登录页</li>
                <li>□ 本地token被清除</li>
                <li>□ 无法直接访问管理页面</li>
            </ul>
        </div>

        <div class="test-step">
            <h3>状态管理</h3>
            <ul>
                <li>□ 页面刷新后用户状态保持</li>
                <li>□ Token过期自动跳转登录</li>
                <li>□ 未登录访问管理页面自动重定向</li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <h2>🚀 快速测试</h2>
        <p>
            <button onclick="testLogin()">开始完整测试流程</button>
            <button onclick="window.open('http://localhost:8000/admin/dashboard', '_blank')">
                直接访问仪表盘（测试重定向）
            </button>
        </p>
    </div>

    <script>
        function testLogin() {
            alert('请按照以下步骤进行测试：\n\n1. 点击"打开登录页面"按钮\n2. 使用 admin/admin123 登录\n3. 检查右上角用户信息\n4. 测试退出登录功能\n5. 使用 editor/123456 测试编辑员角色');
        }
    </script>
</body>
</html>
