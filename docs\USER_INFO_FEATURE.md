# 管理后台用户信息显示功能

## 功能概述

管理后台登录成功后，在页面右上角会显示当前登录用户的信息，包括用户名、角色，以及退出登录按钮。

## 功能特性

### 1. 用户信息显示
- **用户头像**：显示用户名首字母的圆形头像
- **用户名**：显示当前登录用户的用户名
- **用户角色**：显示用户角色（管理员/编辑员）
- **美观设计**：采用卡片式设计，视觉效果良好

### 2. 退出登录功能
- **一键退出**：点击"退出登录"按钮即可退出
- **状态清理**：自动清除本地存储的token和用户状态
- **页面跳转**：退出后自动跳转到登录页面
- **API调用**：调用后台退出登录接口

### 3. 响应式设计
- **鼠标悬停效果**：退出按钮有悬停变色效果
- **自适应布局**：适配不同屏幕尺寸

## 实现细节

### 布局配置
在 `src/app.ts` 的 `layout` 配置中实现：

```typescript
rightContentRender: () => {
  // 获取当前用户信息
  const { currentUser } = initialState || {};
  
  if (!currentUser) {
    return null;
  }

  // 渲染用户信息组件
  return React.createElement(/* 用户信息UI */);
}
```

### 用户信息结构
```typescript
{
  id: number;
  username: string;
  role: 'admin' | 'editor';
  enabled: boolean;
  createdAt: string;
  updatedAt: string;
}
```

### 角色显示映射
- `admin` → "管理员"
- `editor` → "编辑员"

## 使用说明

### 1. 登录后查看
1. 使用测试账号登录管理后台
2. 登录成功后，页面右上角会显示用户信息
3. 用户信息包含头像、用户名和角色

### 2. 退出登录
1. 点击右上角的红色"退出登录"按钮
2. 系统会自动：
   - 调用后台退出登录API
   - 清除本地存储的token
   - 清除全局用户状态
   - 跳转到登录页面

### 3. 状态恢复
- 页面刷新时会自动恢复用户登录状态
- 如果token有效，用户信息会重新显示
- 如果token无效，会自动跳转到登录页

## 测试步骤

### 基本功能测试
1. **登录测试**
   ```
   访问：http://localhost:8000/admin/login
   账号：admin / admin123
   验证：登录后右上角显示用户信息
   ```

2. **用户信息显示测试**
   ```
   验证项：
   - 头像显示用户名首字母
   - 用户名正确显示
   - 角色正确显示（管理员）
   - 退出按钮正常显示
   ```

3. **退出登录测试**
   ```
   操作：点击"退出登录"按钮
   验证：
   - 跳转到登录页面
   - 本地token被清除
   - 无法直接访问管理页面
   ```

### 不同角色测试
1. **管理员角色**
   ```
   账号：admin / admin123
   预期：显示"管理员"角色
   ```

2. **编辑员角色**
   ```
   账号：editor / 123456
   预期：显示"编辑员"角色
   ```

### 状态恢复测试
1. **页面刷新测试**
   ```
   操作：登录后刷新页面
   验证：用户信息仍然显示
   ```

2. **直接访问测试**
   ```
   操作：未登录时直接访问管理页面
   验证：自动跳转到登录页
   ```

## 样式说明

### 用户信息卡片
- 背景色：`rgba(0, 0, 0, 0.02)`
- 边框：`1px solid #f0f0f0`
- 圆角：`6px`
- 内边距：`8px 16px`

### 用户头像
- 尺寸：`32px × 32px`
- 背景色：`#1890ff`
- 文字颜色：白色
- 字体大小：`14px`
- 字体粗细：粗体

### 退出按钮
- 背景色：`#ff4d4f`
- 悬停色：`#ff7875`
- 文字颜色：白色
- 圆角：`4px`
- 内边距：`6px 12px`

## 技术实现

### 状态管理
- 使用 Umi 的 `initialState` 管理用户状态
- 通过 `useModel('@@initialState')` 获取和更新状态

### 认证流程
1. 登录成功后保存用户信息到 `initialState`
2. 布局组件从 `initialState` 读取用户信息
3. 退出时清除 `initialState` 中的用户信息

### API集成
- 登录时调用 `/admin/auth/login`
- 退出时调用 `/admin/auth/logout`
- 自动token刷新机制

## 注意事项

1. **安全性**
   - 退出登录会完全清除本地状态
   - Token过期会自动跳转登录页

2. **用户体验**
   - 登录状态在页面刷新后保持
   - 退出操作即时生效

3. **兼容性**
   - 支持不同角色用户
   - 适配移动端显示

4. **扩展性**
   - 可以轻松添加更多用户信息
   - 支持自定义用户操作菜单
