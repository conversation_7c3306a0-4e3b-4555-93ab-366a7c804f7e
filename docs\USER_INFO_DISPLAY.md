# 用户信息显示功能说明

## 功能概述

管理后台登录成功后，在仪表盘页面会显示当前登录用户的信息，包括用户名、角色，以及退出登录按钮。

## 实现位置

用户信息显示在以下位置：

### 1. 仪表盘页面顶部
- **位置**: `/admin/dashboard` 页面顶部
- **样式**: 渐变背景的卡片式设计
- **内容**: 
  - 用户头像（显示用户名首字母）
  - 欢迎信息和用户名
  - 用户角色显示
  - 当前日期
  - 退出登录按钮

### 2. 布局右上角（备用方案）
- **位置**: 页面右上角
- **样式**: 下拉菜单式
- **内容**: 用户头像、用户名、退出登录选项

## 功能特性

### 用户信息显示
- ✅ **用户头像**: 圆形头像显示用户名首字母
- ✅ **用户名**: 显示当前登录用户的用户名
- ✅ **角色标识**: 显示用户角色（管理员/编辑员）
- ✅ **欢迎信息**: 个性化的欢迎文字
- ✅ **日期显示**: 显示当前日期

### 退出登录功能
- ✅ **一键退出**: 红色退出按钮，醒目易用
- ✅ **状态清理**: 自动清除本地token和用户状态
- ✅ **页面跳转**: 退出后自动跳转到登录页面
- ✅ **API调用**: 调用后台退出登录接口
- ✅ **消息提示**: 退出成功/失败的消息提示

### 视觉设计
- ✅ **渐变背景**: 美观的紫色渐变背景
- ✅ **响应式布局**: 适配不同屏幕尺寸
- ✅ **悬停效果**: 按钮有悬停变色效果
- ✅ **图标支持**: 使用Ant Design图标

## 测试步骤

### 1. 登录测试
```
1. 访问: http://localhost:8000/admin/login
2. 输入账号: admin / admin123
3. 点击登录按钮
4. 验证跳转到仪表盘页面
```

### 2. 用户信息显示验证
```
登录成功后，在仪表盘页面顶部应该看到：
- 紫色渐变背景的用户信息卡片
- 左侧显示用户头像（字母 "A"）
- 中间显示 "欢迎回来，admin！"
- 显示 "角色：管理员 | 今天是 [当前日期]"
- 右侧显示红色的"退出登录"按钮
```

### 3. 退出登录测试
```
1. 点击右侧红色的"退出登录"按钮
2. 验证是否显示"退出登录成功"消息
3. 验证是否跳转到登录页面
4. 尝试直接访问 /admin/dashboard，验证是否被重定向到登录页
```

### 4. 不同角色测试
```
1. 使用编辑员账号登录: editor / 123456
2. 验证角色显示为 "编辑员"
3. 验证其他功能正常
```

## 技术实现

### 状态管理
```typescript
const { initialState, setInitialState } = useModel('@@initialState');
const { currentUser } = initialState || {};
```

### 退出登录处理
```typescript
const handleLogout = async () => {
  try {
    await logout();
    await setInitialState({
      name: '智慧营建系统',
      currentUser: null,
      isAdmin: false,
    });
    message.success('退出登录成功');
  } catch (error) {
    message.error('退出登录失败');
  }
};
```

### 用户信息显示
```jsx
{currentUser && (
  <Card style={{ background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' }}>
    <Row align="middle" justify="space-between">
      <Col>
        <Space size="large">
          <Avatar size={64}>{currentUser.username.charAt(0).toUpperCase()}</Avatar>
          <div>
            <Title level={3}>欢迎回来，{currentUser.username}！</Title>
            <p>角色：{currentUser.role === 'admin' ? '管理员' : '编辑员'}</p>
          </div>
        </Space>
      </Col>
      <Col>
        <Button danger onClick={handleLogout}>退出登录</Button>
      </Col>
    </Row>
  </Card>
)}
```

## 样式配置

### 卡片样式
- **背景**: `linear-gradient(135deg, #667eea 0%, #764ba2 100%)`
- **文字颜色**: 白色
- **边距**: `marginBottom: 24px`

### 头像样式
- **尺寸**: `64px × 64px`
- **背景**: `rgba(255, 255, 255, 0.2)`
- **文字**: 白色，24px，粗体

### 按钮样式
- **类型**: `primary danger`
- **尺寸**: `large`
- **背景**: `rgba(255, 77, 79, 0.8)`

## 注意事项

1. **用户状态检查**: 只有在用户已登录时才显示用户信息卡片
2. **错误处理**: 退出登录失败时会显示错误消息
3. **状态同步**: 退出登录后会完全清除全局用户状态
4. **页面跳转**: 退出后自动跳转到登录页面

## 扩展功能

可以考虑添加的功能：
- 用户个人信息编辑
- 密码修改功能
- 登录历史记录
- 用户偏好设置
- 头像上传功能

## 故障排除

如果用户信息不显示：
1. 检查是否成功登录
2. 检查 `initialState` 中是否有 `currentUser` 数据
3. 检查浏览器控制台是否有错误
4. 验证token是否有效
