// 运行时配置

import {
  AntdConfig,
  RequestConfig,
  RuntimeAntdConfig,
  history,
  request as httpRequest,
} from '@umijs/max';
import { message } from 'antd';
import { getToken, refreshToken, removeToken, setToken } from './utils/auth';

// 全局初始化数据配置，用于 Layout 用户信息和权限初始化
// 更多信息见文档：https://umijs.org/docs/api/runtime-config#getinitialstate
export async function getInitialState(): Promise<{
  name: string;
  currentUser?: any;
  isAdmin?: boolean;
}> {
  const token = getToken();

  // 如果有token，尝试获取用户信息
  if (token) {
    try {
      const { getCurrentUser } = await import('./services/auth');
      const response = await getCurrentUser();

      if (response.errCode === 0 && response.data) {
        return {
          name: '智慧营建系统',
          currentUser: response.data,
          isAdmin: response.data.role === 'admin',
        };
      }
    } catch (error) {
      console.error('获取用户信息失败:', error);
      // 如果获取用户信息失败，清除token
      removeToken();
    }
  }

  return {
    name: '智慧营建系统',
    currentUser: null,
    isAdmin: false,
  };
}

// 路由变化时的权限检查
export function onRouteChange({ location }: any) {
  const { pathname } = location;

  // 如果是管理端路由，检查登录状态
  if (pathname.startsWith('/admin') && pathname !== '/admin/login') {
    const token = getToken();
    if (!token) {
      // 未登录，跳转到登录页
      history.push('/admin/login');
      return;
    }
  }
}

// 管理端才使用layout布局
export const layout = () => {
  return {
    logo: '/logo.png',
    title: '智慧营建管理系统',
    menu: {
      locale: false,
    },
    // 只在管理端路由下显示layout
    disableContentMargin: false,
    // 右上角用户信息
    rightContentRender: () => {
      const React = require('react');

      // 动态导入UserInfo组件
      const UserInfoComponent = React.lazy(
        () => import('./components/UserInfo'),
      );

      return React.createElement(
        React.Suspense,
        { fallback: React.createElement('div', null, '加载中...') },
        React.createElement(UserInfoComponent),
      );
    },
  };
};

export const antd: RuntimeAntdConfig = (
  memo: AntdConfig & { [key: string]: any },
) => {
  // 按需加载
  memo.import = true;

  // 配置 antd 的 App 包裹组件
  memo.appConfig = {
    // dark: true,
    message: {
      // 配置 message 最大显示数，超过限制时，最早的消息会被自动关闭
      maxCount: 1,
    },
  };

  memo.theme ??= {
    components: {
      Segmented: {
        itemSelectedBg: '#2979ff',
        itemSelectedColor: '#fff',
      },
      Collapse: {
        headerBg: '#2979ff',
      },
    },
  };
  memo.theme.token = {
    colorPrimary: '#2979ff', // 主题色
  };

  return memo;
};

export const request: RequestConfig = {
  baseURL: '/api',
  timeout: 20000,
  headers: { 'X-Requested-With': 'XMLHttpRequest' },
  errorConfig: {
    errorHandler: async (error: any) => {
      const { response, config } = error;

      // 添加一个标记，用于识别是否是重试请求
      if (config._isRetry) {
        message.error(response?.data?.message || '请求失败');
        removeToken();
        return Promise.reject(error);
      }

      if (response?.status === 401) {
        // 如果是登录或刷新token接口，直接返回错误
        if (
          config.url.includes('/admin/auth/login') ||
          config.url.includes('/admin/auth/refresh')
        ) {
          message.error(response?.data?.message || '登录失败');
          return Promise.reject(error);
        }

        // 其他接口尝试刷新token
        try {
          const newToken = await refreshToken();
          if (newToken) {
            // 重试失败的请求
            config.headers.Authorization = `Bearer ${newToken}`;
            // 标记这是一个重试请求
            config._isRetry = true;
            return httpRequest(config.url, {
              ...config,
              headers: config.headers,
            });
          }
        } catch (e) {
          // 刷新token失败，清除token并跳转登录
          removeToken();
          message.error('登录已过期，请重新登录');
          history.push('/admin/login');
          return Promise.reject(error);
        }
      } else {
        message.error(response?.data?.message || '请求失败');
        return Promise.reject(error);
      }
    },
  },
  requestInterceptors: [
    (config: any) => {
      const token = getToken();
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    },
  ],
  responseInterceptors: [
    (response) => {
      // 处理header中的新token
      const newToken = response.headers['new-token'];
      if (newToken) {
        setToken(newToken);
      }
      return response;
    },
  ],
};
