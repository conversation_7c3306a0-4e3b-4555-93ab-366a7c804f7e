import { request } from '@umijs/max';

// 认证相关的API接口

/**
 * 登录接口
 */
export async function login(params: {
  username: string;
  password: string;
}): Promise<API.ResType<{
  token: string;
  user: {
    id: number;
    username: string;
    role: string;
    enabled: boolean;
    createdAt: string;
    updatedAt: string;
  };
}>> {
  return request('/admin/auth/login', {
    method: 'POST',
    data: params,
  });
}

/**
 * 获取当前用户信息
 */
export async function getCurrentUser(): Promise<API.ResType<{
  id: number;
  username: string;
  role: string;
  permissions: string[];
}>> {
  return request('/admin/auth/profile', {
    method: 'GET',
  });
}

/**
 * 退出登录
 */
export async function logout(): Promise<API.ResType<{
  message: string;
}>> {
  return request('/admin/auth/logout', {
    method: 'POST',
  });
}

/**
 * 刷新Token
 */
export async function refreshToken(): Promise<API.ResType<{
  message: string;
}>> {
  return request('/admin/auth/refresh', {
    method: 'POST',
  });
}
