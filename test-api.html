<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            background: #f5f5f5;
            padding: 20px;
            margin: 15px 0;
            border-radius: 8px;
            border-left: 4px solid #1890ff;
        }
        button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover {
            background: #40a9ff;
        }
        .result {
            background: #f6f8fa;
            padding: 15px;
            border-radius: 6px;
            margin: 10px 0;
            border: 1px solid #e1e4e8;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            border-left: 4px solid #52c41a;
            background: #f6ffed;
        }
        .error {
            border-left: 4px solid #ff4d4f;
            background: #fff2f0;
        }
    </style>
</head>
<body>
    <h1>🔧 API接口测试</h1>
    
    <div class="test-section">
        <h2>登录接口测试</h2>
        <p>测试登录API是否正常工作</p>
        <button onclick="testLogin()">测试管理员登录</button>
        <button onclick="testEditorLogin()">测试编辑员登录</button>
        <button onclick="testWrongLogin()">测试错误登录</button>
        <div id="loginResult" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h2>用户信息接口测试</h2>
        <p>测试获取用户信息API（需要先登录）</p>
        <button onclick="testGetProfile()">获取用户信息</button>
        <div id="profileResult" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h2>退出登录接口测试</h2>
        <p>测试退出登录API</p>
        <button onclick="testLogout()">退出登录</button>
        <div id="logoutResult" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h2>Token刷新接口测试</h2>
        <p>测试Token刷新API</p>
        <button onclick="testRefreshToken()">刷新Token</button>
        <div id="refreshResult" class="result" style="display: none;"></div>
    </div>

    <script>
        let currentToken = null;

        async function makeRequest(url, options = {}) {
            try {
                const response = await fetch(url, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...(currentToken ? { 'Authorization': `Bearer ${currentToken}` } : {}),
                        ...options.headers
                    },
                    ...options
                });

                const data = await response.json();
                return {
                    status: response.status,
                    data: data,
                    success: response.ok
                };
            } catch (error) {
                return {
                    status: 0,
                    data: { message: error.message },
                    success: false
                };
            }
        }

        function showResult(elementId, result, isSuccess = true) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `result ${isSuccess ? 'success' : 'error'}`;
            element.textContent = JSON.stringify(result, null, 2);
        }

        async function testLogin() {
            const result = await makeRequest('/api/admin/auth/login', {
                method: 'POST',
                body: JSON.stringify({
                    username: 'admin',
                    password: 'admin123'
                })
            });

            if (result.success && result.data.data && result.data.data.token) {
                currentToken = result.data.data.token;
                console.log('Token saved:', currentToken);
            }

            showResult('loginResult', result, result.success);
        }

        async function testEditorLogin() {
            const result = await makeRequest('/api/admin/auth/login', {
                method: 'POST',
                body: JSON.stringify({
                    username: 'editor',
                    password: '123456'
                })
            });

            if (result.success && result.data.data && result.data.data.token) {
                currentToken = result.data.data.token;
                console.log('Token saved:', currentToken);
            }

            showResult('loginResult', result, result.success);
        }

        async function testWrongLogin() {
            const result = await makeRequest('/api/admin/auth/login', {
                method: 'POST',
                body: JSON.stringify({
                    username: 'wrong',
                    password: 'wrong'
                })
            });

            showResult('loginResult', result, false);
        }

        async function testGetProfile() {
            if (!currentToken) {
                showResult('profileResult', { error: '请先登录获取token' }, false);
                return;
            }

            const result = await makeRequest('/api/admin/auth/profile', {
                method: 'GET'
            });

            showResult('profileResult', result, result.success);
        }

        async function testLogout() {
            if (!currentToken) {
                showResult('logoutResult', { error: '请先登录获取token' }, false);
                return;
            }

            const result = await makeRequest('/api/admin/auth/logout', {
                method: 'POST'
            });

            if (result.success) {
                currentToken = null;
                console.log('Token cleared');
            }

            showResult('logoutResult', result, result.success);
        }

        async function testRefreshToken() {
            if (!currentToken) {
                showResult('refreshResult', { error: '请先登录获取token' }, false);
                return;
            }

            const result = await makeRequest('/api/admin/auth/refresh', {
                method: 'POST'
            });

            showResult('refreshResult', result, result.success);
        }

        // 页面加载时显示当前状态
        window.onload = function() {
            console.log('API测试页面已加载');
            console.log('当前Token:', currentToken);
        };
    </script>
</body>
</html>
