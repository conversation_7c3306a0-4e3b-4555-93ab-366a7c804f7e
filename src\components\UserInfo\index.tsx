import { logout } from '@/utils/auth';
import { LogoutOutlined, UserOutlined } from '@ant-design/icons';
import { useModel } from '@umijs/max';
import { Avatar, Dropdown, Space, Typography, message } from 'antd';
import React from 'react';

const { Text } = Typography;

const UserInfo: React.FC = () => {
  const { initialState, setInitialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};

  if (!currentUser) {
    return (
      <Space style={{ padding: '8px 12px' }}>
        <Avatar size="small" icon={<UserOutlined />} />
        <Text>未登录</Text>
      </Space>
    );
  }

  const handleLogout = async () => {
    try {
      await logout();
      // 清除全局状态
      await setInitialState({
        name: '智慧营建系统',
        currentUser: null,
        isAdmin: false,
      });
      message.success('退出登录成功');
    } catch (error) {
      console.error('退出登录失败:', error);
      message.error('退出登录失败');
    }
  };

  const menuItems = [
    {
      key: 'user-info',
      label: (
        <div
          style={{
            padding: '8px 0',
            textAlign: 'center',
            borderBottom: '1px solid #f0f0f0',
          }}
        >
          <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>
            {currentUser.username}
          </div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {currentUser.role === 'admin' ? '管理员' : '编辑员'}
          </div>
        </div>
      ),
      disabled: true,
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: handleLogout,
    },
  ];

  return (
    <Dropdown
      menu={{ items: menuItems }}
      placement="bottomRight"
      trigger={['click']}
    >
      <Space
        style={{
          cursor: 'pointer',
          padding: '8px 12px',
          borderRadius: '6px',
          transition: 'background-color 0.3s',
        }}
        className="user-info-trigger"
      >
        <Avatar
          size="small"
          style={{
            backgroundColor: '#1890ff',
          }}
        >
          {currentUser.username.charAt(0).toUpperCase()}
        </Avatar>
        <Text style={{ fontSize: '14px' }}>{currentUser.username}</Text>
      </Space>
    </Dropdown>
  );
};

export default UserInfo;
