import { logout } from '@/utils/auth';
import { LogoutOutlined, UserOutlined } from '@ant-design/icons';
import { useModel } from '@umijs/max';
import { Avatar, Dropdown, Space, Typography } from 'antd';
import React from 'react';

const { Text } = Typography;

const UserInfo: React.FC = () => {
  const { initialState, setInitialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};

  if (!currentUser) {
    return null;
  }

  const handleLogout = async () => {
    try {
      await logout();
      // 清除全局状态
      await setInitialState({
        name: '智慧营建系统',
        currentUser: null,
        isAdmin: false,
      });
    } catch (error) {
      console.error('退出登录失败:', error);
    }
  };

  const menuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人信息',
      disabled: true, // 暂时禁用，后续可以添加个人信息页面
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: handleLogout,
    },
  ];

  return (
    <Dropdown
      menu={{ items: menuItems }}
      placement="bottomRight"
      arrow
      trigger={['click']}
    >
      <Space
        style={{
          cursor: 'pointer',
          padding: '8px 12px',
          borderRadius: '6px',
          transition: 'background-color 0.3s',
        }}
        className="user-info-trigger"
      >
        <Avatar
          size="small"
          icon={<UserOutlined />}
          style={{
            backgroundColor: '#1890ff',
          }}
        />
        <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start' }}>
          <Text strong style={{ fontSize: '14px', lineHeight: '20px' }}>
            {currentUser.username}
          </Text>
          <Text
            type="secondary"
            style={{ fontSize: '12px', lineHeight: '16px' }}
          >
            {currentUser.role === 'admin' ? '管理员' : '编辑员'}
          </Text>
        </div>
      </Space>
    </Dropdown>
  );
};

export default UserInfo;
