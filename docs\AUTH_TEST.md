# 认证功能测试文档

## 测试环境

- 前端地址：http://localhost:8000
- 管理端登录：http://localhost:8000/admin/login

## 测试账号

### 管理员账号
- 用户名：`admin`
- 密码：`admin123`
- 角色：`admin`
- 权限：所有权限

### 编辑员账号
- 用户名：`editor`
- 密码：`123456`
- 角色：`editor`
- 权限：读取权限

## 测试步骤

### 1. 登录功能测试

1. 访问 http://localhost:8000/admin/login
2. 输入管理员账号：`admin` / `admin123`
3. 点击登录按钮
4. 验证是否跳转到 `/admin/dashboard`
5. 验证右上角是否显示用户信息和退出按钮

### 2. 权限验证测试

1. 登录成功后，验证是否能访问管理端各个页面
2. 检查左侧菜单是否正常显示
3. 验证用户角色是否正确显示

### 3. 退出登录测试

1. 点击右上角的"退出登录"按钮
2. 验证是否跳转到登录页面
3. 验证token是否被清除
4. 尝试直接访问管理端页面，验证是否被重定向到登录页

### 4. Token刷新测试

1. 登录后，等待一段时间
2. 进行API调用，验证token自动刷新机制
3. 检查浏览器开发者工具的网络请求

### 5. 表单验证测试

1. 测试用户名长度验证（3-50字符）
2. 测试密码长度验证（6-50字符）
3. 测试必填字段验证
4. 测试错误用户名/密码的处理

## API接口测试

### 登录接口
```bash
curl -X POST "http://localhost:8000/api/admin/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "admin123"
  }'
```

### 获取用户信息接口
```bash
curl -X GET "http://localhost:8000/api/admin/auth/profile" \
  -H "Authorization: Bearer <token>"
```

### 退出登录接口
```bash
curl -X POST "http://localhost:8000/api/admin/auth/logout" \
  -H "Authorization: Bearer <token>"
```

### 刷新Token接口
```bash
curl -X POST "http://localhost:8000/api/admin/auth/refresh" \
  -H "Authorization: Bearer <token>"
```

## 预期结果

### 登录成功响应
```json
{
  "errCode": 0,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": 1,
      "username": "admin",
      "role": "admin",
      "enabled": true,
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    }
  }
}
```

### 用户信息响应
```json
{
  "errCode": 0,
  "data": {
    "id": 1,
    "username": "admin",
    "role": "admin",
    "permissions": ["*"]
  }
}
```

### 错误响应示例
```json
{
  "message": "用户名或密码错误",
  "statusCode": 401,
  "timestamp": "2024-01-01T00:00:00.000Z",
  "path": "/admin/auth/login"
}
```

## 注意事项

1. 当前使用的是模拟接口，实际部署时需要连接真实的后台服务
2. Token存储在localStorage中，生产环境建议使用更安全的存储方式
3. 所有管理端路由都需要认证，未登录用户会被重定向到登录页
4. Token过期后会自动尝试刷新，刷新失败则跳转到登录页
