import { userData } from '../src/services/mockData';

// 模拟JWT Token
const generateToken = (user: any) => {
  return `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.${btoa(JSON.stringify({
    id: user.id,
    username: user.username,
    role: user.role,
    exp: Date.now() + 24 * 60 * 60 * 1000 // 24小时过期
  }))}.mock_signature`;
};

// 验证Token
const verifyToken = (token: string) => {
  try {
    if (!token || !token.startsWith('eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.')) {
      return null;
    }
    const payload = JSON.parse(atob(token.split('.')[1]));
    if (payload.exp < Date.now()) {
      return null; // Token过期
    }
    return payload;
  } catch {
    return null;
  }
};

export default {
  // 管理员登录
  'POST /api/admin/auth/login': (req: any, res: any) => {
    const { username, password } = req.body;

    // 参数验证
    if (!username || !password) {
      return res.status(400).json({
        message: '用户名和密码不能为空',
        statusCode: 400,
        timestamp: new Date().toISOString(),
        path: '/admin/auth/login'
      });
    }

    if (username.length < 3 || username.length > 50) {
      return res.status(400).json({
        message: '用户名长度为3-50字符',
        statusCode: 400,
        timestamp: new Date().toISOString(),
        path: '/admin/auth/login'
      });
    }

    if (password.length < 6 || password.length > 50) {
      return res.status(400).json({
        message: '密码长度为6-50字符',
        statusCode: 400,
        timestamp: new Date().toISOString(),
        path: '/admin/auth/login'
      });
    }

    // 查找用户
    const user = userData.find(
      (u) => u.username === username && u.password === password
    );

    if (!user) {
      return res.status(401).json({
        message: '用户名或密码错误',
        statusCode: 401,
        timestamp: new Date().toISOString(),
        path: '/admin/auth/login'
      });
    }

    if (!user.enabled) {
      return res.status(401).json({
        message: '账户已被禁用',
        statusCode: 401,
        timestamp: new Date().toISOString(),
        path: '/admin/auth/login'
      });
    }

    // 生成Token
    const token = generateToken(user);

    res.json({
      errCode: 0,
      data: {
        token,
        user: {
          id: user.id,
          username: user.username,
          role: user.role,
          enabled: user.enabled,
          createdAt: user.createdAt,
          updatedAt: user.updatedAt
        }
      }
    });
  },

  // 获取当前用户信息
  'GET /api/admin/auth/profile': (req: any, res: any) => {
    const authorization = req.headers.authorization;
    if (!authorization || !authorization.startsWith('Bearer ')) {
      return res.status(401).json({
        message: '认证令牌无效或已过期',
        statusCode: 401,
        timestamp: new Date().toISOString(),
        path: '/admin/auth/profile'
      });
    }

    const token = authorization.substring(7);
    const payload = verifyToken(token);

    if (!payload) {
      return res.status(401).json({
        message: '认证令牌无效或已过期',
        statusCode: 401,
        timestamp: new Date().toISOString(),
        path: '/admin/auth/profile'
      });
    }

    const user = userData.find(u => u.id === payload.id);
    if (!user) {
      return res.status(401).json({
        message: '用户不存在',
        statusCode: 401,
        timestamp: new Date().toISOString(),
        path: '/admin/auth/profile'
      });
    }

    res.json({
      errCode: 0,
      data: {
        id: user.id,
        username: user.username,
        role: user.role,
        permissions: user.role === 'admin' ? ['*'] : ['read']
      }
    });
  },

  // 退出登录
  'POST /api/admin/auth/logout': (req: any, res: any) => {
    const authorization = req.headers.authorization;
    if (!authorization || !authorization.startsWith('Bearer ')) {
      return res.status(401).json({
        message: '认证令牌无效或已过期',
        statusCode: 401,
        timestamp: new Date().toISOString(),
        path: '/admin/auth/logout'
      });
    }

    res.json({
      errCode: 0,
      data: {
        message: '退出成功'
      }
    });
  },

  // 刷新Token
  'POST /api/admin/auth/refresh': (req: any, res: any) => {
    const authorization = req.headers.authorization;
    if (!authorization || !authorization.startsWith('Bearer ')) {
      return res.status(401).json({
        message: '认证令牌无效或已过期',
        statusCode: 401,
        timestamp: new Date().toISOString(),
        path: '/admin/auth/refresh'
      });
    }

    const token = authorization.substring(7);
    const payload = verifyToken(token);

    if (!payload) {
      return res.status(401).json({
        message: '认证令牌无效或已过期',
        statusCode: 401,
        timestamp: new Date().toISOString(),
        path: '/admin/auth/refresh'
      });
    }

    const user = userData.find(u => u.id === payload.id);
    if (!user) {
      return res.status(401).json({
        message: '用户不存在',
        statusCode: 401,
        timestamp: new Date().toISOString(),
        path: '/admin/auth/refresh'
      });
    }

    // 生成新Token
    const newToken = generateToken(user);
    
    // 在响应头中返回新Token
    res.set('new-token', newToken);
    
    res.json({
      errCode: 0,
      data: {
        message: 'Token刷新成功'
      }
    });
  },
};
